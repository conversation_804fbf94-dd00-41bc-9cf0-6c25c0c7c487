#!/usr/bin/env python3
"""
Simple test to check FastAPI backend
"""

import requests

# Test health endpoint (no auth required)
print("Testing health endpoint...")
try:
    response = requests.get("http://localhost:8000/health")
    print(f"Health check status: {response.status_code}")
    print(f"Health check response: {response.json()}")
except Exception as e:
    print(f"Health check failed: {e}")

# Test authenticated endpoint
print("\nTesting authenticated endpoint...")
try:
    auth = ("demo", "demo123")
    response = requests.get("http://localhost:8000/api/scenarios", auth=auth)
    print(f"Scenarios status: {response.status_code}")
    if response.status_code == 200:
        print(f"Scenarios response: {response.json()}")
    else:
        print(f"Scenarios error: {response.text}")
except Exception as e:
    print(f"Scenarios test failed: {e}")
