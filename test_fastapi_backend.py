#!/usr/bin/env python3
"""
Test script for FastAPI backend with SQLite3
Tests all API endpoints to ensure they work correctly
"""

import requests
import json
import time
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000"
AUTH = ("demo", "demo123")  # Basic Auth credentials

def make_request(endpoint, method="GET", data=None):
    """Make HTTP request to the API"""
    url = f"{BASE_URL}{endpoint}"
    headers = {"Content-Type": "application/json"}

    print(f"🔍 Making {method} request to {url} with auth {AUTH}")

    try:
        if method == "GET":
            response = requests.get(url, auth=AUTH, headers=headers)
        elif method == "POST":
            response = requests.post(url, auth=AUTH, headers=headers, json=data)
        elif method == "PUT":
            response = requests.put(url, auth=AUTH, headers=headers, json=data)
        elif method == "DELETE":
            response = requests.delete(url, auth=AUTH, headers=headers)

        print(f"🔍 Response status: {response.status_code}")
        print(f"🔍 Response headers: {dict(response.headers)}")

        if response.status_code in [200, 201]:
            return response.json()
        else:
            print(f"❌ Request failed: {response.status_code} - {response.text}")
            return None
    except requests.exceptions.ConnectionError:
        print(f"❌ Connection failed to {url}")
        print("💡 Make sure the FastAPI server is running: python backend/start_server.py")
        return None
    except Exception as e:
        print(f"❌ Request error: {e}")
        return None

def test_health_check():
    """Test health check endpoint"""
    print("\n🏥 Testing Health Check...")
    result = make_request("/health")
    if result and result.get("status") == "healthy":
        print("✅ Health check passed")
        return True
    else:
        print("❌ Health check failed")
        return False

def test_create_scenarios():
    """Test creating scenarios"""
    print("\n📝 Testing Scenario Creation...")
    
    scenarios_data = [
        {
            "name": "As-Is Retirement Planning",
            "category": "as-is",
            "asIsDetails": "Current policy with existing premium structure",
            "whatIfOptions": ["Increase premium by 10%", "Change to annual payments"]
        },
        {
            "name": "Face Amount Optimization",
            "category": "face-amount",
            "asIsDetails": "Optimize death benefit amount",
            "whatIfOptions": ["Increase face amount", "Decrease face amount"]
        },
        {
            "name": "Premium Adjustment Strategy",
            "category": "premium",
            "asIsDetails": "Adjust premium payment strategy",
            "whatIfOptions": ["Monthly payments", "Quarterly payments", "Annual payments"]
        }
    ]
    
    created_ids = []
    for i, scenario_data in enumerate(scenarios_data, 1):
        print(f"   Creating scenario {i}: {scenario_data['name']}")
        result = make_request("/api/scenarios", "POST", scenario_data)
        if result and "id" in result:
            created_ids.append(result["id"])
            print(f"   ✅ Created scenario {i} with ID: {result['id']}")
        else:
            print(f"   ❌ Failed to create scenario {i}")
    
    print(f"\n📊 Created {len(created_ids)} scenarios")
    return created_ids

def test_get_scenarios():
    """Test getting all scenarios"""
    print("\n📋 Testing Get Scenarios...")
    result = make_request("/api/scenarios")
    if result and "scenarios" in result:
        scenarios = result["scenarios"]
        print(f"✅ Retrieved {len(scenarios)} scenarios")
        for scenario in scenarios:
            print(f"   - {scenario['name']} (ID: {scenario['id']})")
        return scenarios
    else:
        print("❌ Failed to get scenarios")
        return []

def test_update_scenario(scenario_id):
    """Test updating a scenario"""
    print(f"\n✏️ Testing Scenario Update for ID: {scenario_id}")
    
    update_data = {
        "name": "Updated Scenario Name",
        "asIsDetails": "Updated details for the scenario"
    }
    
    result = make_request(f"/api/scenarios/{scenario_id}", "PUT", update_data)
    if result and "message" in result:
        print("✅ Scenario updated successfully")
        return True
    else:
        print("❌ Failed to update scenario")
        return False

def test_selected_scenarios(scenario_ids):
    """Test selected scenarios functionality"""
    print("\n🎯 Testing Selected Scenarios...")
    
    # Select first two scenarios
    selected_ids = scenario_ids[:2] if len(scenario_ids) >= 2 else scenario_ids
    
    print(f"   Selecting {len(selected_ids)} scenarios...")
    result = make_request("/api/scenarios/selected", "POST", {
        "selectedScenarios": selected_ids
    })
    
    if result and "message" in result:
        print("✅ Selected scenarios updated")
        
        # Verify selection
        print("   Verifying selection...")
        get_result = make_request("/api/scenarios/selected")
        if get_result and "selectedScenarios" in get_result:
            retrieved_ids = get_result["selectedScenarios"]
            if set(retrieved_ids) == set(selected_ids):
                print(f"✅ Selection verified: {len(retrieved_ids)} scenarios selected")
                return True
            else:
                print(f"❌ Selection mismatch: expected {selected_ids}, got {retrieved_ids}")
        else:
            print("❌ Failed to retrieve selected scenarios")
    else:
        print("❌ Failed to update selected scenarios")
    
    return False

def test_delete_scenario(scenario_id):
    """Test deleting a scenario"""
    print(f"\n🗑️ Testing Scenario Deletion for ID: {scenario_id}")
    
    result = make_request(f"/api/scenarios/{scenario_id}", "DELETE")
    if result and "message" in result:
        print("✅ Scenario deleted successfully")
        return True
    else:
        print("❌ Failed to delete scenario")
        return False

def test_complete_workflow():
    """Test complete workflow"""
    print("🧪 FASTAPI BACKEND TEST - Complete Workflow")
    print("=" * 60)
    
    # Test health check
    if not test_health_check():
        return False
    
    # Create scenarios
    created_ids = test_create_scenarios()
    if not created_ids:
        print("❌ Cannot continue without created scenarios")
        return False
    
    # Get all scenarios
    scenarios = test_get_scenarios()
    if not scenarios:
        print("❌ Cannot continue without retrieving scenarios")
        return False
    
    # Update a scenario
    if not test_update_scenario(created_ids[0]):
        print("⚠️ Scenario update failed, but continuing...")
    
    # Test selected scenarios
    if not test_selected_scenarios(created_ids):
        print("⚠️ Selected scenarios test failed, but continuing...")
    
    # Delete one scenario
    if created_ids and not test_delete_scenario(created_ids[-1]):
        print("⚠️ Scenario deletion failed, but continuing...")
    
    # Final verification
    print("\n🔍 Final Verification...")
    final_scenarios = test_get_scenarios()
    expected_count = len(created_ids) - 1  # One was deleted
    
    if len(final_scenarios) == expected_count:
        print(f"✅ Final count correct: {len(final_scenarios)} scenarios")
        return True
    else:
        print(f"❌ Final count mismatch: expected {expected_count}, got {len(final_scenarios)}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting FastAPI Backend Tests...")
    print(f"🌐 Testing server at: {BASE_URL}")
    print(f"🔐 Using credentials: {AUTH[0]}/{AUTH[1]}")
    print()
    
    if test_complete_workflow():
        print("\n🎉 ALL TESTS PASSED!")
        print("\n✅ FastAPI Backend with SQLite3 is working correctly")
        print("✅ All API endpoints are functional")
        print("✅ Database operations are working")
        print("✅ Authentication is working")
        print("\n🚀 Ready for frontend integration!")
        print("Start the frontend with: npm run dev")
    else:
        print("\n❌ SOME TESTS FAILED")
        print("Check the error messages above and fix any issues")

if __name__ == "__main__":
    main()
