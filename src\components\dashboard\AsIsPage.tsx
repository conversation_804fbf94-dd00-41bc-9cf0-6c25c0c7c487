import React, { useState, useEffect } from 'react';
import { Calculator, Save, Download, ArrowRight } from 'lucide-react';
import Card from '../common/Card';
import Button from '../common/Button';
import Select from '../common/Select';
import Input from '../common/Input';
import { useDashboard } from '../../contexts/DashboardContext';

const AsIsPage: React.FC = () => {
  const [policyData, setPolicyData] = useState({
    policyNumber: '',
    customerName: '',
    customerId: '',
    policyType: '',
    faceAmount: '',
    annualPremium: '',
    paymentPeriod: '',
    dividendOption: '',
    currentAge: '',
    retirementAge: '',
    lifeExpectancy: '',
  });

  // As-Is Illustration Scenarios state
  const initialIllustrationScenarios = {
    modelRetirementGoal: true,
    retirementGoalAge: '65',
    customRetirementAge: '',
    confirmMaturityAge: true,
    maturityAge: '121',
    alternateMaturityAge: '',
  };
  const [illustrationScenarios, setIllustrationScenarios] = useState(initialIllustrationScenarios);

  // Projection results state
  const [projectionResults, setProjectionResults] = useState<any>(null);
  const [showProjections, setShowProjections] = useState(false);

  const { setActiveTab, selectedCustomerData, selectedPolicyData, addScenario } = useDashboard();

  // Populate form data from selected policy and customer data
  useEffect(() => {
    if (selectedCustomerData && selectedPolicyData) {
      // Extract premium amount from string (e.g., "2000 $ annually" -> "2000")
      const premiumMatch = selectedPolicyData.premium.match(/(\d+)/);
      const premiumAmount = premiumMatch ? premiumMatch[1] : '';

      // Extract coverage amount from string (e.g., "500,000 $" -> "500000")
      const coverageMatch = selectedPolicyData.coverage.replace(/,/g, '').match(/(\d+)/);
      const coverageAmount = coverageMatch ? coverageMatch[1] : '';

      // Calculate current age from DOB (assuming DOB format is DD.MM.YYYY)
      const calculateAge = (dobString: string) => {
        const [day, month, year] = dobString.split('.').map(Number);
        const birthDate = new Date(year, month - 1, day);
        const today = new Date();
        let age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
          age--;
        }
        return age.toString();
      };

      setPolicyData({
        policyNumber: selectedCustomerData.details["Policy Number"] || selectedCustomerData.policyNumber,
        customerName: selectedCustomerData.name,
        customerId: selectedCustomerData.details["Customer ID"] || selectedCustomerData.customerId,
        policyType: selectedPolicyData.name.toLowerCase().replace(/\s+/g, '-'),
        faceAmount: coverageAmount,
        annualPremium: premiumAmount,
        paymentPeriod: '20', // Default value - could be enhanced with actual data
        dividendOption: 'Paid-up Additions', // Default value - could be enhanced with actual data
        currentAge: calculateAge(selectedCustomerData.details.DOB),
        retirementAge: '65', // Default value - could be enhanced based on customer preferences
        lifeExpectancy: '85', // Default value - could be enhanced based on actuarial data
      });

      // Set intelligent defaults for illustration scenarios based on customer age
      const defaultRetirementAge = parseInt(calculateAge(selectedCustomerData.details.DOB)) < 50 ? '65' : parseInt(calculateAge(selectedCustomerData.details.DOB)) < 60 ? '67' : '70';
      setIllustrationScenarios(prev => ({
        ...initialIllustrationScenarios,
        retirementGoalAge: defaultRetirementAge,
        maturityAge: '121', // Standard maturity age
      }));
    }
  }, [selectedCustomerData, selectedPolicyData]);

  const policyTypes = [
    { value: 'whole-life', label: 'Whole Life Insurance' },
    { value: 'term-life', label: 'Term Life Insurance' },
    { value: 'universal-life', label: 'Universal Life Insurance' },
    { value: 'variable-life', label: 'Variable Life Insurance' },
    { value: 'indexed-universal', label: 'Indexed Universal Life' },
  ];

  const dividendOptions = [
    { value: 'Cash', label: 'Cash' },
    { value: 'Accumulate', label: 'Accumulate' },
    { value: 'Paid-up Additions', label: 'Paid-up Additions' },
    { value: 'Reduce Premium', label: 'Reduce Premium' },
  ];

  const handlePolicyDataChange = (field: string, value: string) => {
    setPolicyData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleIllustrationScenarioChange = (field: string, value: string | boolean) => {
    setIllustrationScenarios(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const validateForm = () => {
    const errors = [];

    // Validate policy data
    if (!policyData.policyNumber) errors.push('Policy Number is required');
    if (!policyData.customerName) errors.push('Customer Name is required');
    if (!policyData.customerId) errors.push('Customer ID is required');
    if (!policyData.faceAmount) errors.push('Face Amount is required');
    if (!policyData.annualPremium) errors.push('Annual Premium is required');
    if (!policyData.currentAge) errors.push('Current Age is required');

    // Validate illustration scenarios
    if (illustrationScenarios.modelRetirementGoal) {
      if (illustrationScenarios.retirementGoalAge === 'custom' && !illustrationScenarios.customRetirementAge) {
        errors.push('Custom retirement age is required when selected');
      }
    }

    if (illustrationScenarios.confirmMaturityAge && !illustrationScenarios.maturityAge) {
      errors.push('Maturity age is required when confirmed');
    }

    return errors;
  };

  const handleSaveAsIs = async () => {
    const errors = validateForm();
    if (errors.length > 0) {
      alert('Please fix the following errors:\n' + errors.join('\n'));
      return;
    }

    // Save AS-IS configuration
    const configData = {
      policyData,
      illustrationScenarios,
      timestamp: new Date().toISOString()
    };

    console.log('Saving AS-IS configuration:', configData);

    // Save to localStorage for persistence
    localStorage.setItem('asIsConfiguration', JSON.stringify(configData));

    // Create scenario for selected scenarios tab
    const newScenario = {
      id: Date.now().toString() + Math.random(),
      name: `As-Is Configuration - ${policyData.customerName || 'Policy'} (${policyData.policyNumber || 'N/A'})`,
      policyId: policyData.policyNumber || 'N/A',
      asIsDetails: `Retirement Age: ${illustrationScenarios.retirementGoalAge}, Maturity Age: ${illustrationScenarios.maturityAge}`,
      whatIfOptions: [
        `Face Amount: ${policyData.faceAmount ? parseInt(policyData.faceAmount).toLocaleString() : 'N/A'}`,
        `Annual Premium: ${policyData.annualPremium ? parseInt(policyData.annualPremium).toLocaleString() : 'N/A'}`,
      ],
      category: 'as-is' as const,
      data: configData,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Add to selected scenarios
    try {
      await addScenario(newScenario);
      alert('AS-IS configuration saved successfully and added to Selected Scenarios!');
    } catch (error) {
      alert('Error saving AS-IS configuration. Please try again.');
      console.error('Error saving AS-IS scenario:', error);
    }
  };

  const handleResetScenarios = () => {
    setIllustrationScenarios(initialIllustrationScenarios);
    setShowProjections(false);
    setProjectionResults(null);
  };

  const generateProjectionData = () => {
    const startYear = new Date().getFullYear();
    const endYear = parseInt(illustrationScenarios.maturityAge); // Project to maturity age
    const projectionRate = 0.055; // Default projection rate
    const inflationRate = 0.025; // Default inflation rate
    const dividendRate = 0.062; // Default dividend rate

    const faceAmount = parseInt(policyData.faceAmount) || 0;
    const annualPremium = parseInt(policyData.annualPremium) || 0;
    const currentAge = parseInt(policyData.currentAge) || 0;
    const retirementAge = parseInt(illustrationScenarios.retirementGoalAge) || 65;

    const projectionYears = [];
    let cashValue = 0;
    let deathBenefit = faceAmount;
    let totalPremiumsPaid = 0;

    for (let year = startYear; year <= endYear; year++) {
      const policyYear = year - startYear + 1;
      const insuredAge = currentAge + policyYear - 1;

      // Calculate premium payments (stop at retirement age if configured)
      const premiumPayment = insuredAge < retirementAge ? annualPremium : 0;
      totalPremiumsPaid += premiumPayment;

      // Calculate cash value growth
      const previousCashValue = cashValue;
      cashValue = (previousCashValue + premiumPayment) * (1 + projectionRate);

      // Apply dividend if applicable
      if (dividendRate > 0) {
        cashValue += cashValue * dividendRate;
      }

      // Calculate death benefit (may increase with dividends)
      deathBenefit = Math.max(faceAmount, faceAmount * (1 + dividendRate * policyYear * 0.1));

      // Calculate surrender value (cash value minus surrender charges)
      const surrenderChargeRate = Math.max(0, 0.1 - (policyYear * 0.01)); // Decreasing surrender charge
      const surrenderValue = cashValue * (1 - surrenderChargeRate);

      projectionYears.push({
        year,
        policyYear,
        insuredAge,
        premiumPayment,
        cashValue: Math.round(cashValue),
        deathBenefit: Math.round(deathBenefit),
        surrenderValue: Math.round(surrenderValue),
        totalPremiumsPaid: Math.round(totalPremiumsPaid),
        netCashFlow: Math.round(cashValue - totalPremiumsPaid)
      });
    }

    return {
      projectionYears,
      summary: {
        totalPremiumsPaid: Math.round(totalPremiumsPaid),
        finalCashValue: Math.round(cashValue),
        finalDeathBenefit: Math.round(deathBenefit),
        netReturn: Math.round(cashValue - totalPremiumsPaid),
        projectionPeriod: `${startYear} - ${endYear}`,
        retirementAge,
        maturityAge: parseInt(illustrationScenarios.maturityAge)
      }
    };
  };

  const handleGenerateProjection = () => {
    const errors = validateForm();
    if (errors.length > 0) {
      alert('Please fix the following errors:\n' + errors.join('\n'));
      return;
    }

    // Generate comprehensive projection data
    const projectionData = generateProjectionData();
    setProjectionResults(projectionData);
    setShowProjections(true);

    console.log('Generated AS-IS projection:', projectionData);

    // Scroll to projections section after a brief delay
    setTimeout(() => {
      const projectionsElement = document.getElementById('projections-section');
      if (projectionsElement) {
        projectionsElement.scrollIntoView({ behavior: 'smooth' });
      }
    }, 100);

    alert('AS-IS projection generated successfully! The results are displayed below.');
  };

  const handleProceedToWhatIf = () => {
    setActiveTab('face-amount');
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">AS-IS ILLUSTRATION</h1>
        <p className="text-gray-600 dark:text-gray-400">Configure the current policy details for baseline illustration.</p>
      </div>

      {/* Flow Indicator */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center justify-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
              ✓
            </div>
            <span className="text-sm font-medium text-green-600 dark:text-green-400">1. Policy Information</span>
          </div>
          <div className="w-8 h-1 bg-green-500"></div>
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
              2
            </div>
            <span className="text-sm font-medium text-blue-600 dark:text-blue-400">2. As-Is Scenarios</span>
          </div>
        </div>
      </div>

      {/* Show message if no policy is selected */}
      {(!selectedCustomerData || !selectedPolicyData) ? (
        <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 dark:text-yellow-400 text-sm">!</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">No Policy Selected</h3>
              <p className="text-yellow-700 dark:text-yellow-300">
                Please go to the Policy Selection tab first to search and select a customer policy before configuring the As-Is illustration.
              </p>
              <Button
                onClick={() => setActiveTab('policy-selection')}
                variant="outline"
                className="mt-3 border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-600 dark:text-yellow-300 dark:hover:bg-yellow-900/40"
              >
                Go to Policy Selection
              </Button>
            </div>
          </div>
        </Card>
      ) : (
        <>
          {/* Current Policy Information */}
          <Card>
            <div className="flex items-center space-x-3 mb-6">
              <Calculator className="w-6 h-6 text-blue-600" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Current Policy Information</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Policy Number</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{policyData.policyNumber || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Customer Name</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{policyData.customerName || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Customer ID</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{policyData.customerId || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Policy Type</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{policyData.policyType || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Face Amount ($)</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{policyData.faceAmount || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Annual Premium ($)</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{policyData.annualPremium || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Payment Period (Years)</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{policyData.paymentPeriod || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Dividend Option</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{policyData.dividendOption || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Current Age</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{policyData.currentAge || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Retirement Age</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{policyData.retirementAge || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Life Expectancy</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{policyData.lifeExpectancy || 'N/A'}</p>
              </div>
            </div>
          </Card>

          {/* As-Is Illustration Scenarios */}
          <Card>
            <div className="flex items-center space-x-3 mb-6">
              <Calculator className="w-6 h-6 text-green-600" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">As-Is Illustration Scenarios</h3>
            </div>

            <div className="space-y-6">
              {/* Project the current policy As-Is */}
              <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                <h4 className="text-md font-semibold text-gray-900 dark:text-gray-100 mb-4">
                  1. Project the current policy As-Is
                </h4>

                {/* Retirement Goal Age */}
                <div className="mb-4">
                  <div className="flex items-center space-x-3 mb-3">
                    <input
                      type="checkbox"
                      id="modelRetirementGoal"
                      checked={illustrationScenarios.modelRetirementGoal}
                      onChange={(e) => handleIllustrationScenarioChange('modelRetirementGoal', e.target.checked)}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <label htmlFor="modelRetirementGoal" className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      Yes, model Specify retirement goal age
                    </label>
                  </div>

                  {illustrationScenarios.modelRetirementGoal && (
                    <div className="ml-7 space-y-3">
                      <div className="flex flex-wrap gap-2">
                        {['60', '62', '65', '67', '70'].map((age) => (
                          <label key={age} className="flex items-center space-x-2">
                            <input
                              type="radio"
                              name="retirementGoalAge"
                              value={age}
                              checked={illustrationScenarios.retirementGoalAge === age}
                              onChange={(e) => handleIllustrationScenarioChange('retirementGoalAge', e.target.value)}
                              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500"
                            />
                            <span className="text-sm text-gray-700 dark:text-gray-300">Age {age}</span>
                          </label>
                        ))}
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          name="retirementGoalAge"
                          value="custom"
                          checked={illustrationScenarios.retirementGoalAge === 'custom'}
                          onChange={(e) => handleIllustrationScenarioChange('retirementGoalAge', e.target.value)}
                          className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500"
                        />
                        <span className="text-sm text-gray-700 dark:text-gray-300">Custom age:</span>
                        <Input
                          type="number"
                          value={illustrationScenarios.customRetirementAge}
                          onChange={(e) => handleIllustrationScenarioChange('customRetirementAge', e.target.value)}
                          placeholder="Enter age"
                          className="w-20"
                          disabled={illustrationScenarios.retirementGoalAge !== 'custom'}
                        />
                      </div>
                    </div>
                  )}
                </div>

                {/* Maturity Age */}
                <div>
                  <div className="flex items-center space-x-3 mb-3">
                    <input
                      type="checkbox"
                      id="confirmMaturityAge"
                      checked={illustrationScenarios.confirmMaturityAge}
                      onChange={(e) => handleIllustrationScenarioChange('confirmMaturityAge', e.target.checked)}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <label htmlFor="confirmMaturityAge" className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      Yes, model Confirm maturity age for this product
                    </label>
                  </div>

                  {illustrationScenarios.confirmMaturityAge && (
                    <div className="ml-7 space-y-3">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-700 dark:text-gray-300">Age</span>
                        <Input
                          type="number"
                          value={illustrationScenarios.maturityAge}
                          onChange={(e) => handleIllustrationScenarioChange('maturityAge', e.target.value)}
                          placeholder="121"
                          className="w-20"
                        />
                      </div>

                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-700 dark:text-gray-300">Alternate maturity age (if applicable): Age</span>
                        <Input
                          type="number"
                          value={illustrationScenarios.alternateMaturityAge}
                          onChange={(e) => handleIllustrationScenarioChange('alternateMaturityAge', e.target.value)}
                          placeholder="Enter age"
                          className="w-20"
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </Card>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-4 justify-center">
            <Button
              onClick={handleSaveAsIs}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <Save className="w-4 h-4" />
              <span>Save AS-IS Illustration</span>
            </Button>
            <Button
              onClick={handleResetScenarios}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <span>Reset Scenarios</span>
            </Button>
            <Button
              onClick={handleProceedToWhatIf}
              className="flex items-center space-x-2"
            >
              <span>Proceed to Face Amount Illustration</span>
              <ArrowRight className="w-4 h-4" />
            </Button>
          </div>

          {/* Projection Results */}
          {showProjections && projectionResults && (
            <div id="projections-section">
              <Card className="mt-8 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-green-200 dark:border-green-800">
              <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-6">AS-IS Projection Results</h3>

              {/* Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div className="text-center p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                  <p className="text-sm text-gray-600 dark:text-gray-400">Total Premiums Paid</p>
                  <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    ${projectionResults.summary.totalPremiumsPaid.toLocaleString()}
                  </p>
                </div>
                <div className="text-center p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                  <p className="text-sm text-gray-600 dark:text-gray-400">Final Cash Value</p>
                  <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                    ${projectionResults.summary.finalCashValue.toLocaleString()}
                  </p>
                </div>
                <div className="text-center p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                  <p className="text-sm text-gray-600 dark:text-gray-400">Final Death Benefit</p>
                  <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                    ${projectionResults.summary.finalDeathBenefit.toLocaleString()}
                  </p>
                </div>
                <div className="text-center p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                  <p className="text-sm text-gray-600 dark:text-gray-400">Net Return</p>
                  <p className={`text-2xl font-bold ${projectionResults.summary.netReturn >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                    ${projectionResults.summary.netReturn.toLocaleString()}
                  </p>
                </div>
              </div>

              {/* Projection Table */}
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300 dark:border-gray-600">
                  <thead className="bg-gray-100 dark:bg-gray-700">
                    <tr>
                      <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left">Year</th>
                      <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left">Age</th>
                      <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-right">Premium</th>
                      <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-right">Cash Value</th>
                      <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-right">Death Benefit</th>
                      <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-right">Surrender Value</th>
                      <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-right">Net Cash Flow</th>
                    </tr>
                  </thead>
                  <tbody>
                    {projectionResults.projectionYears.slice(0, 20).map((yearData: any, index: number) => (
                      <tr key={index} className={index % 2 === 0 ? 'bg-white dark:bg-gray-800' : 'bg-gray-50 dark:bg-gray-750'}>
                        <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">{yearData.year}</td>
                        <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">{yearData.insuredAge}</td>
                        <td className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-right">
                          ${yearData.premiumPayment.toLocaleString()}
                        </td>
                        <td className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-right">
                          ${yearData.cashValue.toLocaleString()}
                        </td>
                        <td className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-right">
                          ${yearData.deathBenefit.toLocaleString()}
                        </td>
                        <td className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-right">
                          ${yearData.surrenderValue.toLocaleString()}
                        </td>
                        <td className={`border border-gray-300 dark:border-gray-600 px-4 py-2 text-right ${yearData.netCashFlow >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          ${yearData.netCashFlow.toLocaleString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {projectionResults.projectionYears.length > 20 && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-2 text-center">
                    Showing first 20 years of {projectionResults.projectionYears.length} total projection years
                  </p>
                )}
              </div>
            </Card>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default AsIsPage;