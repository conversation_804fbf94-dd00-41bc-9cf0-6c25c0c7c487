import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, <PERSON>ltip, Legend, ResponsiveContainer } from 'recharts';
import { Save, RotateCcw, BarChart3, TrendingUp, User, FileText, DollarSign, Calendar, AlertTriangle } from 'lucide-react';
import Card from '../common/Card';
import Button from '../common/Button';
import { useDashboard } from '../../contexts/DashboardContext';

const FaceAmountPage = () => {
  // Add dashboard context for policy info
  const { selectedCustomerData, selectedPolicyData, setActiveTab, addScenario } = useDashboard();

  // Mock customer data - in real app this would come from props or context
  const [customerData] = useState({
    name: '<PERSON>',
    customer_id: 'CUST001',
    policy_number: 'POL123456',
    coverage: '$500,000',
    premium: '$2,500/year'
  });

  const [selectedPolicy] = useState({
    name: 'Universal Life',
    coverage: '$500,000',
    premium: '$2,500/year'
  });

  const [faceAmountData, setFaceAmountData] = useState({
    current_death_benefit: 500000,
    want_to_change: false,
    change_immediately: false,
    change_amount: 0,
    change_by_years: false,
    change_year: '',
    death_benefit_option: 'A',
    switch_option: false,
    switch_immediately: false,
    switch_future_year: false,
    switch_year: '',
    switch_condition: false,
    condition_age: '',
    condition_cash_value: '',
    keep_current_option: true,
    option_b_to_a: false,
    option_b_switch_immediately: false,
    option_b_switch_future: false,
    option_b_switch_year: '',
    option_b_switch_condition: false,
    option_b_condition_age: '',
    option_b_condition_cash_value: '',
    keep_option_b: true
  });

  type FaceAmountScenario = {
    id: number;
    timestamp: string;
    customer_name: string;
    policy_number: string;
    data: typeof faceAmountData;
    summary: string[];
  };
  const [faceAmountHistory, setFaceAmountHistory] = useState<FaceAmountScenario[]>([]);
  const [showIllustration, setShowIllustration] = useState(false);
  const [showComparison, setShowComparison] = useState(false);
  type NotificationType = { message: string; type?: 'success' | 'error' };
  const [notification, setNotification] = useState<NotificationType | null>(null);

  // Show notification temporarily
  const showNotification = (message: string, type: 'success' | 'error' = 'success') => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 3000);
  };

  // Update form data
  const updateFormData = (field: keyof typeof faceAmountData, value: any) => {
    setFaceAmountData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Save scenario
  const saveScenario = async () => {
    try {
      const now = new Date();
      const scenarioEntry = {
        id: (Date.now().toString() + Math.random()),
        name: `Face Amount Scenario - ${(selectedCustomerData?.name || customerData.name)}`,
        policyId: selectedCustomerData?.policyNumber || customerData.policy_number,
        asIsDetails: `Current Death Benefit: $${faceAmountData.current_death_benefit.toLocaleString()}`,
        whatIfOptions: generateScenarioSummary(),
        category: 'face-amount' as const,
        data: { ...faceAmountData },
        createdAt: now,
        updatedAt: now,
      };
      await addScenario(scenarioEntry);
      showNotification('Face Amount scenario saved and added to Selected Scenarios!');
    } catch (error) {
      showNotification('Error saving scenario!', 'error');
      console.error('Error saving Face Amount scenario:', error);
    }
  };

  // Generate scenario summary
  const generateScenarioSummary = () => {
    const summary = [];
    
    if (faceAmountData.want_to_change) {
      if (faceAmountData.change_immediately) {
        summary.push(`Change death benefit to $${faceAmountData.change_amount.toLocaleString()} immediately`);
      }
      if (faceAmountData.change_by_years) {
        summary.push(`Change death benefit by year: ${faceAmountData.change_year}`);
      }
    }
    
    if (faceAmountData.switch_immediately) {
      summary.push("Switch from Option A to B immediately");
    }
    if (faceAmountData.switch_future_year) {
      summary.push(`Switch from Option A to B in year: ${faceAmountData.switch_year}`);
    }
    if (faceAmountData.switch_condition) {
      summary.push(`Switch A to B when age ${faceAmountData.condition_age} or cash value $${faceAmountData.condition_cash_value}`);
    }
    
    if (faceAmountData.option_b_switch_immediately) {
      summary.push("Switch from Option B to A immediately");
    }
    if (faceAmountData.option_b_switch_future) {
      summary.push(`Switch from Option B to A in year: ${faceAmountData.option_b_switch_year}`);
    }
    if (faceAmountData.option_b_switch_condition) {
      summary.push(`Switch B to A when age ${faceAmountData.option_b_condition_age} or cash value $${faceAmountData.option_b_condition_cash_value}`);
    }
    
    return summary.length > 0 ? summary : ["No changes selected"];
  };

  // Reset form
  const resetForm = () => {
    setFaceAmountData({
      current_death_benefit: 500000,
      want_to_change: false,
      change_immediately: false,
      change_amount: 0,
      change_by_years: false,
      change_year: '',
      death_benefit_option: 'A',
      switch_option: false,
      switch_immediately: false,
      switch_future_year: false,
      switch_year: '',
      switch_condition: false,
      condition_age: '',
      condition_cash_value: '',
      keep_current_option: true,
      option_b_to_a: false,
      option_b_switch_immediately: false,
      option_b_switch_future: false,
      option_b_switch_year: '',
      option_b_switch_condition: false,
      option_b_condition_age: '',
      option_b_condition_cash_value: '',
      keep_option_b: true
    });
    showNotification('Form reset!');
  };

  // Generate illustration data
  const generateIllustrationData = () => {
    const years = Array.from({ length: 20 }, (_, i) => 2024 + i);
    
    return years.map(year => {
      const yearsFromNow = year - 2024;
      const currentValue = faceAmountData.current_death_benefit * Math.pow(1.02, yearsFromNow);
      const newValue = faceAmountData.want_to_change && faceAmountData.change_immediately
        ? faceAmountData.change_amount * Math.pow(1.02, yearsFromNow)
        : currentValue;
      
      return {
        year,
        currentBenefit: Math.round(currentValue),
        newBenefit: Math.round(newValue)
      };
    });
  };

  // Generate illustration
  const generateIllustration = () => {
    setShowIllustration(true);
    showNotification('Face Amount illustration generated successfully!');
  };

  // Load scenario
  const loadScenario = (scenario: any) => {
    setFaceAmountData({ ...scenario.data });
    showNotification(`Scenario ${scenario.id} loaded!`);
  };

  // Add new state for change_by_years options
  const [changeByYearsOptions, setChangeByYearsOptions] = useState({ policyYear: false, age: false, calendarYear: false });
  const [changeByYearsValues, setChangeByYearsValues] = useState({ policyYear: '', age: '', calendarYear: '' });
  // Add new state for switch_future_year options
  const [switchFutureYearOptions, setSwitchFutureYearOptions] = useState({ policyYear: false, age: false, calendarYear: false });
  const [switchFutureYearValues, setSwitchFutureYearValues] = useState({ policyYear: '', age: '', calendarYear: '' });

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      {/* Main Title (match AsIsPage style) */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">FACE AMOUNT ILLUSTRATION</h1>
        <p className="text-gray-600 dark:text-gray-400">Configure the face amount and death benefit options for the selected policy.</p>
      </div>
      {/* Stepper/Progress Indicator */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-8">
        <div className="flex items-center justify-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
              ✓
            </div>
            <span className="text-sm font-medium text-green-600 dark:text-green-400">1. Policy Information</span>
          </div>
          <div className="w-8 h-1 bg-green-500"></div>
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
              2
            </div>
            <span className="text-sm font-medium text-blue-600 dark:text-blue-400">2. Premium What-If</span>
          </div>
        </div>
      </div>
      {/* Notification */}
      {notification && (
        <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
          notification.type === 'error' ? 'bg-red-500' : 'bg-green-500'
        } text-white`}>
          {notification.message}
        </div>
      )}

      {/* Show message if no policy is selected */}
      {(!selectedCustomerData || !selectedPolicyData) ? (
        <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 mb-8">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 dark:text-yellow-400 text-sm">!</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">No Policy Selected</h3>
              <p className="text-yellow-700 dark:text-yellow-300">
                Please go to the Policy Selection tab first to search and select a customer policy before configuring the Face Amount illustration.
              </p>
              <Button
                onClick={() => setActiveTab('policy-selection')}
                variant="outline"
                className="mt-3 border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-600 dark:text-yellow-300 dark:hover:bg-yellow-900/40"
              >
                Go to Policy Selection
              </Button>
            </div>
          </div>
        </Card>
      ) : (
        <>
          {/* Current Policy Information */}
          <Card className="mb-8">
            <div className="flex items-center space-x-3 mb-6">
              <User className="w-6 h-6 text-blue-600" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Current Policy Information</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Policy Number</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{selectedCustomerData.policyNumber || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Customer Name</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{selectedCustomerData.name || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Customer ID</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{selectedCustomerData.customerId || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Policy Type</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{selectedPolicyData.name || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Current Coverage</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{selectedPolicyData.coverage || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Current Premium</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{selectedPolicyData.premium || 'N/A'}</p>
              </div>
            </div>
          </Card>

          {/* Section 1: Current Death Benefit Changes */}
          <div className="bg-white p-8 rounded-xl shadow-lg mb-8 border-l-6 border-blue-500">
            <h3 className="text-2xl font-bold text-black mb-6 pb-4 border-b-2 border-gray-200">
              1. Current Death Benefit Changes
            </h3>
            
            <div className="grid grid-cols-2 gap-6 mb-6">
              <div>
                <label className="block text-sm font-bold text-black mb-2">
                  Your current (Face Amount) Death Benefit is:
                </label>
                <input
                  type="number"
                  value={faceAmountData.current_death_benefit}
                  onChange={(e) => updateFormData('current_death_benefit', parseInt(e.target.value) || 0)}
                  className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50"
                  step="10000"
                />
              </div>
              <div className="flex items-center">
                <label className="flex items-center text-black font-semibold">
                  <input
                    type="checkbox"
                    checked={faceAmountData.want_to_change}
                    onChange={(e) => updateFormData('want_to_change', e.target.checked)}
                    className="mr-2"
                  />
                  Do you want to change it?
                </label>
              </div>
            </div>

            {faceAmountData.want_to_change && (
              <div className="bg-gray-50 p-6 rounded-lg border">
                <h4 className="font-bold text-black mb-4">Change Options:</h4>
                
                <div className="space-y-4">
                  <div>
                    <label className="flex items-center text-black font-semibold mb-2">
                      <input
                        type="checkbox"
                        checked={faceAmountData.change_immediately}
                        onChange={(e) => updateFormData('change_immediately', e.target.checked)}
                        className="mr-2"
                      />
                      Yes, to
                    </label>
                    {faceAmountData.change_immediately && (
                      <div className="grid grid-cols-2 gap-4 mt-2">
                        <div>
                          <label className="block text-sm font-bold text-black mb-1">
                            New Death Benefit Amount:
                          </label>
                          <input
                            type="number"
                            value={faceAmountData.change_amount}
                            onChange={(e) => updateFormData('change_amount', parseInt(e.target.value) || 0)}
                            className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50"
                            step="10000"
                          />
                        </div>
                        <div className="flex items-center">
                          <span className="font-bold text-black">Immediately</span>
                        </div>
                      </div>
                    )}
                  </div>

                  <div>
                    <label className="flex items-center text-black font-semibold mb-2">
                      <input
                        type="checkbox"
                        checked={faceAmountData.change_by_years}
                        onChange={(e) => updateFormData('change_by_years', e.target.checked)}
                        className="mr-2"
                      />
                      Yes, change the Death Benefit by years – Policy Year/Age/Calendar Year
                    </label>
                    {faceAmountData.change_by_years && (
                      <div className="grid grid-cols-2 gap-4 mt-2">
                        <div>
                          <label className="block text-sm font-bold text-black mb-1">Select Options:</label>
                          <div className="flex gap-4 mb-2">
                            <label className="flex items-center text-black">
                              <input type="checkbox" checked={changeByYearsOptions.policyYear} onChange={e => setChangeByYearsOptions(prev => ({ ...prev, policyYear: e.target.checked }))} className="mr-1" /> Policy Year
                            </label>
                            <label className="flex items-center text-black">
                              <input type="checkbox" checked={changeByYearsOptions.age} onChange={e => setChangeByYearsOptions(prev => ({ ...prev, age: e.target.checked }))} className="mr-1" /> Age
                            </label>
                            <label className="flex items-center text-black">
                              <input type="checkbox" checked={changeByYearsOptions.calendarYear} onChange={e => setChangeByYearsOptions(prev => ({ ...prev, calendarYear: e.target.checked }))} className="mr-1" /> Calendar Year
                            </label>
                          </div>
                          {changeByYearsOptions.policyYear && (
                            <input type="text" placeholder="Policy Year" value={changeByYearsValues.policyYear} onChange={e => setChangeByYearsValues(prev => ({ ...prev, policyYear: e.target.value }))} className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50 mb-2" />
                          )}
                          {changeByYearsOptions.age && (
                            <input type="text" placeholder="Age" value={changeByYearsValues.age} onChange={e => setChangeByYearsValues(prev => ({ ...prev, age: e.target.value }))} className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50 mb-2" />
                          )}
                          {changeByYearsOptions.calendarYear && (
                            <input type="text" placeholder="Calendar Year" value={changeByYearsValues.calendarYear} onChange={e => setChangeByYearsValues(prev => ({ ...prev, calendarYear: e.target.value }))} className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50 mb-2" />
                          )}
                        </div>
                        <div className="flex items-center">
                          <button onClick={() => showNotification('Death benefit change scheduled successfully!')} className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-600 transition-colors flex items-center gap-2">
                            <Calendar className="w-4 h-4" />
                            Schedule
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Section 2: Death Benefit Option A to B Switch */}
          <div className="bg-white p-8 rounded-xl shadow-lg mb-8 border-l-6 border-blue-500">
            <h3 className="text-2xl font-bold text-black mb-6 pb-4 border-b-2 border-gray-200">
              2. Death Benefit Option Switch (A to B)
            </h3>
            
            <p className="text-black font-semibold mb-6">
              Your current Death Benefit Option is A (Level). Do you want to switch Option A to B (level to increasing)?
            </p>

            <div className="space-y-4">
              <label className="flex items-center text-black font-semibold">
                <input
                  type="checkbox"
                  checked={faceAmountData.switch_immediately}
                  onChange={(e) => updateFormData('switch_immediately', e.target.checked)}
                  className="mr-2"
                />
                Yes, switch immediately
              </label>

              <div>
                <label className="flex items-center text-black font-semibold mb-2">
                  <input
                    type="checkbox"
                    checked={faceAmountData.switch_future_year}
                    onChange={(e) => updateFormData('switch_future_year', e.target.checked)}
                    className="mr-2"
                  />
                  Yes, switch at a future year – Policy Year/Age/Calendar Year
                </label>
                {faceAmountData.switch_future_year && (
                  <div className="grid grid-cols-2 gap-4 mt-2">
                    <div>
                      <label className="block text-sm font-bold text-black mb-1">Select Options:</label>
                      <div className="flex gap-4 mb-2">
                        <label className="flex items-center text-black">
                          <input type="checkbox" checked={switchFutureYearOptions.policyYear} onChange={e => setSwitchFutureYearOptions(prev => ({ ...prev, policyYear: e.target.checked }))} className="mr-1" /> Policy Year
                        </label>
                        <label className="flex items-center text-black">
                          <input type="checkbox" checked={switchFutureYearOptions.age} onChange={e => setSwitchFutureYearOptions(prev => ({ ...prev, age: e.target.checked }))} className="mr-1" /> Age
                        </label>
                        <label className="flex items-center text-black">
                          <input type="checkbox" checked={switchFutureYearOptions.calendarYear} onChange={e => setSwitchFutureYearOptions(prev => ({ ...prev, calendarYear: e.target.checked }))} className="mr-1" /> Calendar Year
                        </label>
                      </div>
                      {switchFutureYearOptions.policyYear && (
                        <input type="text" placeholder="Policy Year" value={switchFutureYearValues.policyYear} onChange={e => setSwitchFutureYearValues(prev => ({ ...prev, policyYear: e.target.value }))} className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50 mb-2" />
                      )}
                      {switchFutureYearOptions.age && (
                        <input type="text" placeholder="Age" value={switchFutureYearValues.age} onChange={e => setSwitchFutureYearValues(prev => ({ ...prev, age: e.target.value }))} className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50 mb-2" />
                      )}
                      {switchFutureYearOptions.calendarYear && (
                        <input type="text" placeholder="Calendar Year" value={switchFutureYearValues.calendarYear} onChange={e => setSwitchFutureYearValues(prev => ({ ...prev, calendarYear: e.target.value }))} className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50 mb-2" />
                      )}
                    </div>
                    <div className="flex items-center">
                      <button onClick={() => showNotification('Option switch scheduled successfully!')} className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-600 transition-colors flex items-center gap-2">
                        <Calendar className="w-4 h-4" />
                        Schedule
                      </button>
                    </div>
                  </div>
                )}
              </div>

              <div>
                <label className="flex items-center text-black font-semibold mb-2">
                  <input
                    type="checkbox"
                    checked={faceAmountData.switch_condition}
                    onChange={(e) => updateFormData('switch_condition', e.target.checked)}
                    className="mr-2"
                  />
                  Yes, switch based on condition (e.g., age __, cash value reaches $____)
                </label>
                {faceAmountData.switch_condition && (
                  <div className="grid grid-cols-2 gap-4 mt-2">
                    <div>
                      <label className="block text-sm font-bold text-black mb-1">
                        Age:
                      </label>
                      <input
                        type="text"
                        value={faceAmountData.condition_age}
                        onChange={(e) => updateFormData('condition_age', e.target.value)}
                        className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-bold text-black mb-1">
                        Cash Value ($):
                      </label>
                      <input
                        type="text"
                        value={faceAmountData.condition_cash_value}
                        onChange={(e) => updateFormData('condition_cash_value', e.target.value)}
                        className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50"
                      />
                    </div>
                  </div>
                )}
              </div>

              <label className="flex items-center text-black font-semibold">
                <input
                  type="checkbox"
                  checked={faceAmountData.keep_current_option}
                  onChange={(e) => updateFormData('keep_current_option', e.target.checked)}
                  className="mr-2"
                />
                No, keep current Option A (Level)
              </label>
            </div>
          </div>

          {/* Section 3: Death Benefit Option B to A Switch */}
          <div className="bg-white p-8 rounded-xl shadow-lg mb-8 border-l-6 border-blue-500">
            <h3 className="text-2xl font-bold text-black mb-6 pb-4 border-b-2 border-gray-200">
              3. Death Benefit Option Switch (B to A)
            </h3>
            
            <p className="text-black font-semibold mb-6">
              Your current Death Benefit Option is B (Increasing). Switch Option B to A (Increasing to Level)
            </p>

            <div className="space-y-4">
              <label className="flex items-center text-black font-semibold">
                <input
                  type="checkbox"
                  checked={faceAmountData.option_b_switch_immediately}
                  onChange={(e) => updateFormData('option_b_switch_immediately', e.target.checked)}
                  className="mr-2"
                />
                Yes, switch immediately
              </label>

              <div>
                <label className="flex items-center text-black font-semibold mb-2">
                  <input
                    type="checkbox"
                    checked={faceAmountData.option_b_switch_future}
                    onChange={(e) => updateFormData('option_b_switch_future', e.target.checked)}
                    className="mr-2"
                  />
                  Yes, switch at a future year - Policy Year/Age/Calendar Year
                </label>
                {faceAmountData.option_b_switch_future && (
                  <div className="grid grid-cols-2 gap-4 mt-2">
                    <div>
                      <label className="block text-sm font-bold text-black mb-1">Select Options:</label>
                      <div className="flex gap-4 mb-2">
                        <label className="flex items-center text-black">
                          <input type="checkbox" checked={switchFutureYearOptions.policyYear} onChange={e => setSwitchFutureYearOptions(prev => ({ ...prev, policyYear: e.target.checked }))} className="mr-1" /> Policy Year
                        </label>
                        <label className="flex items-center text-black">
                          <input type="checkbox" checked={switchFutureYearOptions.age} onChange={e => setSwitchFutureYearOptions(prev => ({ ...prev, age: e.target.checked }))} className="mr-1" /> Age
                        </label>
                        <label className="flex items-center text-black">
                          <input type="checkbox" checked={switchFutureYearOptions.calendarYear} onChange={e => setSwitchFutureYearOptions(prev => ({ ...prev, calendarYear: e.target.checked }))} className="mr-1" /> Calendar Year
                        </label>
                      </div>
                      {switchFutureYearOptions.policyYear && (
                        <input type="text" placeholder="Policy Year" value={switchFutureYearValues.policyYear} onChange={e => setSwitchFutureYearValues(prev => ({ ...prev, policyYear: e.target.value }))} className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50 mb-2" />
                      )}
                      {switchFutureYearOptions.age && (
                        <input type="text" placeholder="Age" value={switchFutureYearValues.age} onChange={e => setSwitchFutureYearValues(prev => ({ ...prev, age: e.target.value }))} className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50 mb-2" />
                      )}
                      {switchFutureYearOptions.calendarYear && (
                        <input type="text" placeholder="Calendar Year" value={switchFutureYearValues.calendarYear} onChange={e => setSwitchFutureYearValues(prev => ({ ...prev, calendarYear: e.target.value }))} className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50 mb-2" />
                      )}
                    </div>
                    <div className="flex items-center">
                      <button onClick={() => showNotification('Option B switch scheduled successfully!')} className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-600 transition-colors flex items-center gap-2">
                        <Calendar className="w-4 h-4" />
                        Schedule
                      </button>
                    </div>
                  </div>
                )}
              </div>

              <div>
                <label className="flex items-center text-black font-semibold mb-2">
                  <input
                    type="checkbox"
                    checked={faceAmountData.option_b_switch_condition}
                    onChange={(e) => updateFormData('option_b_switch_condition', e.target.checked)}
                    className="mr-2"
                  />
                  Yes, switch based on condition (e.g., age __, cash value reaches $____)
                </label>
                {faceAmountData.option_b_switch_condition && (
                  <div className="grid grid-cols-2 gap-4 mt-2">
                    <div>
                      <label className="block text-sm font-bold text-black mb-1">
                        Age:
                      </label>
                      <input
                        type="text"
                        value={faceAmountData.option_b_condition_age}
                        onChange={(e) => updateFormData('option_b_condition_age', e.target.value)}
                        className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-bold text-black mb-1">
                        Cash Value ($):
                      </label>
                      <input
                        type="text"
                        value={faceAmountData.option_b_condition_cash_value}
                        onChange={(e) => updateFormData('option_b_condition_cash_value', e.target.value)}
                        className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50"
                      />
                    </div>
                  </div>
                )}
              </div>

              <label className="flex items-center text-black font-semibold">
                <input
                  type="checkbox"
                  checked={faceAmountData.keep_option_b}
                  onChange={(e) => updateFormData('keep_option_b', e.target.checked)}
                  className="mr-2"
                />
                Keep current Option B (Increasing)
              </label>
            </div>
          </div>

          {/* Action Buttons */}
          {/* Remove the old grid of four buttons and add new styled buttons like AsIsPage */}
          <div className="flex flex-wrap gap-4 justify-center mb-8">
            <Button
              onClick={saveScenario}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <Save className="w-4 h-4" />
              <span>Save Face Amount Illustration</span>
            </Button>
            <Button
              onClick={() => { setFaceAmountHistory([]); showNotification('All face amount scenarios reset!'); }}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <RotateCcw className="w-4 h-4" />
              <span>Reset Scenarios</span>
            </Button>
            <Button
              onClick={() => setActiveTab('premium')}
              className="flex items-center space-x-2"
            >
              <span>Proceed to Premium Analysis</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" /></svg>
            </Button>
          </div>

          {/* Illustration Results */}
          {showIllustration && (
            <div className="bg-white p-8 rounded-xl shadow-lg mb-8 border-l-6 border-green-500">
              <h3 className="text-2xl font-bold text-black mb-6 flex items-center gap-2">
                <BarChart3 className="w-6 h-6" />
                Face Amount Illustration Results
              </h3>
              
              <div className="mb-6">
                <h4 className="font-bold text-black mb-2">Current Scenario:</h4>
                <p>• Current Death Benefit: ${faceAmountData.current_death_benefit.toLocaleString()}</p>
                
                {faceAmountData.want_to_change && (
                  <div className="mt-4">
                    <h4 className="font-bold text-black mb-2">Proposed Changes:</h4>
                    {faceAmountData.change_immediately && (
                      <p>• Change to ${faceAmountData.change_amount.toLocaleString()} immediately</p>
                    )}
                    {faceAmountData.change_by_years && (
                      <p>• Change by year: {faceAmountData.change_year}</p>
                    )}
                  </div>
                )}
                
                {(faceAmountData.switch_immediately || faceAmountData.switch_future_year || faceAmountData.switch_condition) && (
                  <div className="mt-4">
                    <h4 className="font-bold text-black mb-2">Option Switches (A to B):</h4>
                    {faceAmountData.switch_immediately && <p>• Switch to Option B immediately</p>}
                    {faceAmountData.switch_future_year && <p>• Switch to Option B in year: {faceAmountData.switch_year}</p>}
                    {faceAmountData.switch_condition && <p>• Switch to Option B when age {faceAmountData.condition_age} or cash value reaches ${faceAmountData.condition_cash_value}</p>}
                  </div>
                )}
              </div>

              <div className="h-96">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={generateIllustrationData()}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="year" />
                    <YAxis tickFormatter={(value: number | string) => `$${(Number(value) / 1000).toFixed(0)}k`} />
                    <Tooltip formatter={(value: number | string) => [`$${Number(value).toLocaleString()}`, '']} />
                    <Legend />
                    <Line 
                      type="monotone" 
                      dataKey="currentBenefit" 
                      stroke="#2563eb" 
                      strokeWidth={3}
                      name="Current Death Benefit"
                    />
                    {faceAmountData.want_to_change && faceAmountData.change_immediately && (
                      <Line 
                        type="monotone" 
                        dataKey="newBenefit" 
                        stroke="#dc2626" 
                        strokeWidth={3}
                        strokeDasharray="10 5"
                        name="New Death Benefit"
                      />
                    )}
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          )}

          {/* Scenario Comparison */}
          {showComparison && (
            <div className="bg-white p-8 rounded-xl shadow-lg mb-8 border-l-6 border-purple-500">
              <h3 className="text-2xl font-bold text-black mb-6 flex items-center gap-2">
                <TrendingUp className="w-6 h-6" />
                Face Amount Scenario Comparison
              </h3>
              
              {faceAmountHistory.length > 0 ? (
                <div className="space-y-4">
                  <h4 className="font-bold text-black">Saved Scenarios:</h4>
                  {faceAmountHistory.map((scenario) => (
                    <div key={scenario.id} className="bg-gray-50 p-4 rounded-lg border">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h5 className="font-bold text-black">Scenario {scenario.id} - {scenario.timestamp}</h5>
                          <p className="text-sm text-gray-600">Customer: {scenario.customer_name}</p>
                          <p className="text-sm text-gray-600">Policy: {scenario.policy_number}</p>
                        </div>
                        <button
                          onClick={() => loadScenario(scenario)}
                          className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-600 transition-colors text-sm"
                        >
                          Load Scenario
                        </button>
                      </div>
                      <div>
                        <p className="font-semibold text-black mb-1">Summary:</p>
                        <ul className="text-sm text-gray-700">
                          {scenario.summary.map((item, index) => (
                            <li key={index}>• {item}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
                  <p className="text-gray-600">No saved scenarios available. Save a scenario first to enable comparison.</p>
                </div>
              )}
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default FaceAmountPage;